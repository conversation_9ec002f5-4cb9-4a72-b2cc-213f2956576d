#!/usr/bin/env python3
"""
Market Trend Analysis Engine
Main entry point for analyzing trading chart images
"""

import sys
import json
import os
import traceback
from datetime import datetime
import logging
import numpy as np

# Import analysis modules
from modules.image_processor import ImageProcessor
from modules.ocr_extractor import OCRExtractor
from modules.price_action_analyzer import PriceActionAnalyzer
from modules.smc_analyzer import SMCAnalyzer
from modules.technical_indicators import TechnicalIndicators
from modules.decision_engine import EnhancedDecisionEngine
from modules.advanced_signal_analyzer import AdvancedSignalAnalyzer

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder for numpy types"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

class MarketAnalysisEngine:
    """Main analysis engine that coordinates all analysis modules"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.ocr_extractor = OCRExtractor()
        self.price_action_analyzer = PriceActionAnalyzer()
        self.smc_analyzer = SMCAnalyzer()
        self.technical_indicators = TechnicalIndicators()
        self.decision_engine = EnhancedDecisionEngine()
        self.advanced_signal_analyzer = AdvancedSignalAnalyzer()
        
    def analyze_chart(self, image_path, upload_id):
        """
        Main analysis function that processes a chart image
        
        Args:
            image_path (str): Path to the chart image
            upload_id (str): Upload ID from database
            
        Returns:
            dict: Complete analysis results
        """
        try:
            logger.info(f"Starting analysis for image: {image_path}")
            
            # Step 1: Process and prepare image
            logger.info("Step 1: Processing image...")
            processed_image = self.image_processor.process_image(image_path)
            
            if processed_image is None:
                raise Exception("Failed to process image")
            
            # Step 2: Extract text and price data using OCR
            logger.info("Step 2: Extracting OCR data...")
            ocr_data = self.ocr_extractor.extract_data(processed_image)
            
            # Step 3: Analyze price action patterns
            logger.info("Step 3: Analyzing price action...")
            price_action_signals = self.price_action_analyzer.analyze(processed_image, ocr_data)
            
            # Step 4: Apply Smart Money Concept analysis
            logger.info("Step 4: Analyzing Smart Money Concepts...")
            smc_signals = self.smc_analyzer.analyze(processed_image, ocr_data)
            
            # Step 5: Calculate technical indicators
            logger.info("Step 5: Calculating technical indicators...")
            technical_data = self.technical_indicators.calculate(processed_image, ocr_data)

            # Step 6: Advanced signal analysis
            logger.info("Step 6: Performing advanced signal analysis...")
            advanced_analysis = self.advanced_signal_analyzer.analyze_advanced_signals(
                processed_image, price_action_signals, smc_signals, technical_data, ocr_data
            )

            # Step 7: Make enhanced trading decision
            logger.info("Step 7: Making enhanced trading decision...")
            decision = self.decision_engine.make_decision(
                price_action_signals,
                smc_signals,
                technical_data,
                ocr_data
            )
            
            # Compile results
            results = {
                "success": True,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "ocr_data": {
                    "extracted_text": ocr_data.get("text", ""),
                    "symbol": ocr_data.get("symbol", "UNKNOWN"),
                    "timeframe": ocr_data.get("timeframe", "UNKNOWN"),
                    "current_price": ocr_data.get("current_price", 0.0),
                    "price_data": ocr_data.get("price_data", {}),
                    "chart_metadata": ocr_data.get("metadata", {})
                },
                "analysis": {
                    "recommendation": decision["recommendation"],
                    "confidence_score": decision["confidence"],
                    "confidence_level": decision.get("confidence_level", "unknown"),
                    "confidence_description": decision.get("confidence_description", ""),
                    "entry_price": decision["entry_price"],
                    "stop_loss": decision["stop_loss"],
                    "take_profit": decision["take_profit"],
                    "risk_reward_ratio": decision["risk_reward_ratio"],
                    "position_size": decision.get("position_size", 0.0),
                    "market_regime": decision.get("market_regime", "unknown"),

                    # Enhanced analysis results
                    "signal_reasoning": decision.get("signal_reasoning", {}),
                    "confluence_analysis": decision.get("confluence_analysis", {}),
                    "execution_plan": decision.get("execution_plan", {}),
                    "risk_factors": decision.get("risk_factors", []),
                    "supporting_factors": decision.get("supporting_factors", []),
                    "key_levels": decision.get("key_levels", []),
                    "timeframe_analysis": decision.get("timeframe_analysis", {}),
                    "scenario_analysis": decision.get("scenario_analysis", {}),

                    # Advanced signal analysis
                    "advanced_patterns": advanced_analysis.get("advanced_patterns", []),
                    "multi_timeframe_analysis": advanced_analysis.get("multi_timeframe_analysis", {}),
                    "signal_quality_metrics": advanced_analysis.get("signal_quality_metrics", {}),
                    "analysis_score": advanced_analysis.get("analysis_score", {}),
                    "recommendation_confidence": advanced_analysis.get("recommendation_confidence", {}),
                    "risk_assessment": advanced_analysis.get("risk_assessment", {}),
                    "execution_timing": advanced_analysis.get("execution_timing", {}),

                    # Original analysis data
                    "price_action_signals": price_action_signals,
                    "smc_signals": smc_signals,
                    "rsi_analysis": technical_data.get("rsi", {}),
                    "macd_analysis": technical_data.get("macd", {}),
                    "moving_averages": technical_data.get("moving_averages", {}),
                    "support_resistance": technical_data.get("support_resistance", {}),
                    "analysis_notes": decision.get("notes", ""),
                    "market_structure": smc_signals.get("market_structure", ""),
                    "trend_direction": price_action_signals.get("trend", "NEUTRAL"),

                    # Metadata
                    "analysis_version": "enhanced_v2.0",
                    "enhanced_features": [
                        "Advanced pattern recognition",
                        "Multi-timeframe analysis simulation",
                        "Signal confluence analysis",
                        "Enhanced risk management",
                        "Detailed signal reasoning",
                        "Market regime detection"
                    ]
                }
            }
            
            logger.info("Analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            logger.error(traceback.format_exc())
            
            return {
                "success": False,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "ocr_data": None,
                "analysis": None
            }

def main():
    """Main entry point"""
    try:
        # Check command line arguments
        if len(sys.argv) != 3:
            print(json.dumps({
                "success": False,
                "error": "Usage: python main.py <image_path> <upload_id>",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)
        
        image_path = sys.argv[1]
        upload_id = sys.argv[2]
        
        # Validate image path
        if not os.path.exists(image_path):
            print(json.dumps({
                "success": False,
                "error": f"Image file not found: {image_path}",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)
        
        # Initialize and run analysis
        engine = MarketAnalysisEngine()
        results = engine.analyze_chart(image_path, upload_id)
        
        # Output results as JSON
        print(json.dumps(results, indent=2, cls=NumpyEncoder))
        
        # Exit with appropriate code
        sys.exit(0 if results["success"] else 1)
        
    except Exception as e:
        # Handle any unexpected errors
        error_result = {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "traceback": traceback.format_exc()
        }
        
        print(json.dumps(error_result, indent=2, cls=NumpyEncoder))
        sys.exit(1)

if __name__ == "__main__":
    main()
