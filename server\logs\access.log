{"timestamp":"2025-07-25T17:40:53.942Z","level":"INFO","message":"File uploaded successfully: chart_6f8acaef-2b4e-4ee6-8156-2f6557e66781.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:40:54.365Z","level":"ERROR","message":"Failed to save upload record","error":"Access denied for user 'root'@'localhost' (using password: NO)"}
{"timestamp":"2025-07-25T17:42:40.879Z","level":"INFO","message":"File uploaded successfully: chart_f0365c8e-4080-4508-b5fe-fba7363e6f86.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:42:40.932Z","level":"INFO","message":"Upload record created with ID: 1"}
{"timestamp":"2025-07-25T17:42:46.431Z","level":"INFO","message":"Starting analysis for upload 1"}
{"timestamp":"2025-07-25T17:42:46.433Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f0365c8e-4080-4508-b5fe-fba7363e6f86.png 1"}
{"timestamp":"2025-07-25T17:42:47.060Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:47,060 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f0365c8e-4080-4508-b5fe-fba7363e6f86.png\r\n2025-07-26 00:42:47,060 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:42:47.071Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:47,071 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:42:48.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:48,992 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:42:48.993Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:48,992 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:42:49.144Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,144 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:42:49.144Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,144 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:42:49,144 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:42:49.145Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,145 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:42:49.146Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,146 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:42:49.147Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,146 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:42:49.156Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,157 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:42:49.251Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,249 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-25T17:42:49.252Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,250 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 00:42:49,250 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:42:49.254Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,255 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:42:49.260Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,260 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:42:49.287Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,288 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:42:49.288Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,288 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:42:49,288 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:42:49,288 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:42:49.370Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,370 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:42:49.371Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,370 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:42:49,370 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:42:49,371 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:42:49,371 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:42:49.373Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"1\",\r\n  \"timestamp\": \"2025-07-26T00:42:49.371159\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"triangle\",\r\n          \"area\": 18973.0,\r\n          \"vertices\": 3,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3123,\r\n      \"signal_line\": 0.3123,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:42:49.475Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:42:49.476Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:42:49.485Z","level":"INFO","message":"Analysis completed for upload 1"}
{"timestamp":"2025-07-25T17:43:36.914Z","level":"INFO","message":"File uploaded successfully: chart_840fa8ca-e45c-4dad-900e-7afe9b9a540a.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:43:36.920Z","level":"INFO","message":"Upload record created with ID: 2"}
{"timestamp":"2025-07-25T17:43:36.940Z","level":"INFO","message":"Starting analysis for upload 2"}
{"timestamp":"2025-07-25T17:43:36.941Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_840fa8ca-e45c-4dad-900e-7afe9b9a540a.png 2"}
{"timestamp":"2025-07-25T17:43:37.616Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:37,616 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_840fa8ca-e45c-4dad-900e-7afe9b9a540a.png\r\n2025-07-26 00:43:37,616 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:43:37.629Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:37,629 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:43:39.885Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,884 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:43:39.886Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,884 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:43:39.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,992 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:43:39.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,992 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:43:39,992 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:43:39.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,993 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:43:39.995Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,995 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:43:39.996Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,995 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:43:40.003Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,003 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:43:40.101Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,100 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-25T17:43:40.103Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,101 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 00:43:40,101 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:43:40.108Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,107 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:43:40.114Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,114 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:43:40.139Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,140 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:43:40.141Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,140 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:43:40,140 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:43:40,140 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:43:40.225Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,225 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:43:40.226Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,225 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:43:40,226 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:43:40,226 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:43:40,226 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:43:40.227Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"2\",\r\n  \"timestamp\": \"2025-07-26T00:43:40.226161\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"triangle\",\r\n          \"area\": 18973.0,\r\n          \"vertices\": 3,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3123,\r\n      \"signal_line\": 0.3123,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:43:40.325Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:43:40.326Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:43:40.335Z","level":"INFO","message":"Analysis completed for upload 2"}
{"timestamp":"2025-07-25T17:44:37.078Z","level":"INFO","message":"File uploaded successfully: chart_0408232a-a747-472f-a180-c6639423440d.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:44:37.082Z","level":"INFO","message":"Upload record created with ID: 3"}
{"timestamp":"2025-07-25T17:44:37.307Z","level":"INFO","message":"Starting analysis for upload 3"}
{"timestamp":"2025-07-25T17:44:37.307Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_0408232a-a747-472f-a180-c6639423440d.png 3"}
{"timestamp":"2025-07-25T17:44:37.977Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:37,977 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_0408232a-a747-472f-a180-c6639423440d.png"}
{"timestamp":"2025-07-25T17:44:37.978Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:37,977 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:44:37.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:37,992 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:44:40.319Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,318 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:44:40.321Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,319 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:44:40.440Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,440 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:44:40.441Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,440 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:44:40,441 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:44:40.441Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,441 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:44:40.443Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,442 - __main__ - INFO - Step 3: Analyzing price action...\r\n2025-07-26 00:44:40,442 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:44:40.452Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,451 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:44:40.560Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,559 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-25T17:44:40.561Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,559 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 00:44:40,559 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:44:40.565Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,565 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:44:40.573Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,573 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:44:40.602Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,602 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:44:40.604Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,603 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:44:40,603 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:44:40,603 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:44:40.691Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,690 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:44:40.693Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"3\",\r\n  \"timestamp\": \"2025-07-26T00:44:40.691532\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"triangle\",\r\n          \"area\": 18973.0,\r\n          \"vertices\": 3,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3123,\r\n      \"signal_line\": 0.3123,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:44:40.694Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,691 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:44:40,691 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:44:40,691 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:44:40,691 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:44:40.803Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:44:40.804Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:44:40.812Z","level":"INFO","message":"Analysis completed for upload 3"}
{"timestamp":"2025-07-25T17:46:46.728Z","level":"INFO","message":"File uploaded successfully: chart_fc21fe10-acc0-44e0-833f-fc2fa18e8798.png","originalName":"image.png","size":316272,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:46:46.733Z","level":"INFO","message":"Upload record created with ID: 4"}
{"timestamp":"2025-07-25T17:46:46.751Z","level":"INFO","message":"Starting analysis for upload 4"}
{"timestamp":"2025-07-25T17:46:46.752Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_fc21fe10-acc0-44e0-833f-fc2fa18e8798.png 4"}
{"timestamp":"2025-07-25T17:46:47.369Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:47,369 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_fc21fe10-acc0-44e0-833f-fc2fa18e8798.png"}
{"timestamp":"2025-07-25T17:46:47.370Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:47,369 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:46:47.383Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:47,382 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:46:49.521Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,520 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:46:49.523Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,520 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:46:49.645Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,645 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:46:49.646Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,645 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:46:49,645 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:46:49.646Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,646 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:46:49.647Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,647 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:46:49.648Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,647 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:46:49.654Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,653 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:46:49.747Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,746 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: BULLISH\r\n2025-07-26 00:46:49,746 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts..."}
{"timestamp":"2025-07-25T17:46:49.747Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,746 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:46:49.752Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,751 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:46:49.759Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,758 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:46:49.781Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,781 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:46:49.785Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,781 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:46:49,781 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:46:49,781 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:46:49.881Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,880 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:46:49.883Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"4\",\r\n  \"timestamp\": \"2025-07-26T00:46:49.881269\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.4891174670937706,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"BULLISH\",\r\n      \"trend_strength\": 0.3495409584824366,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.27477047924121833,\r\n      \"price_action_score\": 0.1747704792412183\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3121,\r\n      \"signal_line\": 0.3121,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.01694915254237288,\r\n        \"strength\": 0.1694915254237288\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"BULLISH\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:46:49.883Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,880 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:46:49,881 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:46:49,881 - modules.decision_engine - INFO - Decision completed: HOLD with 0.49 confidence\r\n2025-07-26 00:46:49,881 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:46:49.962Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:46:49.962Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:46:49.969Z","level":"INFO","message":"Analysis completed for upload 4"}
{"timestamp":"2025-07-25T17:47:28.723Z","level":"INFO","message":"File uploaded successfully: chart_13d0833e-cd56-4a22-85e7-70aa972a0ebc.png","originalName":"image.png","size":314582,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:47:28.727Z","level":"INFO","message":"Upload record created with ID: 5"}
{"timestamp":"2025-07-25T17:47:28.741Z","level":"INFO","message":"Starting analysis for upload 5"}
{"timestamp":"2025-07-25T17:47:28.741Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_13d0833e-cd56-4a22-85e7-70aa972a0ebc.png 5"}
{"timestamp":"2025-07-25T17:47:29.347Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:29,346 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_13d0833e-cd56-4a22-85e7-70aa972a0ebc.png"}
{"timestamp":"2025-07-25T17:47:29.347Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:29,346 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:47:29.361Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:29,360 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:47:31.390Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,389 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:47:31.392Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,389 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:47:31.499Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,499 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:47:31.500Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,499 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:47:31,499 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:47:31.500Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,500 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:47:31.502Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,501 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:47:31.502Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,501 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:47:31.509Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,509 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:47:31.592Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,591 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL\r\n2025-07-26 00:47:31,591 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts..."}
{"timestamp":"2025-07-25T17:47:31.593Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,591 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:47:31.598Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,598 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:47:31.603Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,603 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:47:31.625Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,624 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:47:31.626Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,625 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:47:31,625 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:47:31,625 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:47:31.702Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,701 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:47:31.705Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"5\",\r\n  \"timestamp\": \"2025-07-26T00:47:31.702267\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 3195.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.639\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 18505.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3135,\r\n      \"signal_line\": 0.3135,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:47:31.706Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,701 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:47:31,702 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:47:31,702 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:47:31,702 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:47:31.798Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:47:31.798Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:47:31.804Z","level":"INFO","message":"Analysis completed for upload 5"}
{"timestamp":"2025-07-26T05:12:11.697Z","level":"INFO","message":"File uploaded successfully: chart_7405844f-5905-46c7-bc3a-49793d1764a6.png","originalName":"image.png","size":314582,"mimetype":"image/png"}
{"timestamp":"2025-07-26T05:12:11.742Z","level":"INFO","message":"Upload record created with ID: 6"}
{"timestamp":"2025-07-26T05:12:11.762Z","level":"INFO","message":"Starting analysis for upload 6"}
{"timestamp":"2025-07-26T05:12:11.764Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_7405844f-5905-46c7-bc3a-49793d1764a6.png 6"}
{"timestamp":"2025-07-26T05:12:12.380Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:12,380 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_7405844f-5905-46c7-bc3a-49793d1764a6.png"}
{"timestamp":"2025-07-26T05:12:12.381Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:12,380 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-26T05:12:12.394Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:12,393 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-26T05:12:14.221Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,220 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:12:14,221 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-26T05:12:14.318Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,317 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-26T05:12:14.318Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,317 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 12:12:14,318 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-26T05:12:14.319Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,318 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-26T05:12:14.320Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,319 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-26T05:12:14.321Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,319 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-26T05:12:14.328Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,327 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-26T05:12:14.424Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,419 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-26T05:12:14.425Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,420 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:12:14,421 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-26T05:12:14.429Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,428 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-26T05:12:14.438Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,438 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-26T05:12:14.464Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,464 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-26T05:12:14.466Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,464 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:12:14,464 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:12:14,464 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-26T05:12:14.550Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,549 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-26T05:12:14.554Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,549 - __main__ - INFO - Step 6: Performing advanced signal analysis...\r\n2025-07-26 12:12:14,550 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis..."}
{"timestamp":"2025-07-26T05:12:14.583Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,582 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals"}
{"timestamp":"2025-07-26T05:12:14.584Z","level":"WARN","message":"Python stderr: 2025-07-26 12:12:14,582 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:12:14,582 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:12:14,582 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:12:14,583 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:12:14,583 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:12:14,583 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.05 confidence (very_low)\r\n2025-07-26 12:12:14,583 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-26T05:12:14.610Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": false,\r\n  \"error\": \"Unexpected error: Object of type intc is not JSON serializable\",\r\n  \"timestamp\": \"2025-07-26T12:12:14.584297\",\r\n  \"traceback\": \"Traceback (most recent call last):\\n  File \\\"D:\\\\Vicky\\\\project baru\\\\auto-analysis\\\\python_analysis\\\\main.py\\\", line 207, in main\\n    print(json.dumps(results, indent=2))\\n          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\__init__.py\\\", line 238, in dumps\\n    **kw).encode(obj)\\n          ~~~~~~^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 200, in encode\\n    chunks = self.iterencode(o, _one_shot=True)\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 261, in iterencode\\n    return _iterencode(o, 0)\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 180, in default\\n    raise TypeError(f'Object of type {o.__class__.__name__} '\\n                    f'is not JSON serializable')\\nTypeError: Object of type intc is not JSON serializable\\n\"\r\n}"}
{"timestamp":"2025-07-26T05:12:14.706Z","level":"INFO","message":"Python process exited with code 1"}
{"timestamp":"2025-07-26T05:12:14.706Z","level":"ERROR","message":"Python analysis failed","code":1,"stdout":"{\r\n  \"success\": false,\r\n  \"error\": \"Unexpected error: Object of type intc is not JSON serializable\",\r\n  \"timestamp\": \"2025-07-26T12:12:14.584297\",\r\n  \"traceback\": \"Traceback (most recent call last):\\n  File \\\"D:\\\\Vicky\\\\project baru\\\\auto-analysis\\\\python_analysis\\\\main.py\\\", line 207, in main\\n    print(json.dumps(results, indent=2))\\n          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\__init__.py\\\", line 238, in dumps\\n    **kw).encode(obj)\\n          ~~~~~~^^^^^\\n  Fil","stderr":"2025-07-26 12:12:12,380 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_7405844f-5905-46c7-bc3a-49793d1764a6.png\r\n2025-07-26 12:12:12,380 - __main__ - INFO - Step 1: Processing image...\r\n2025-07-26 12:12:12,393 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)\r\n2025-07-26 12:12:14,220 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:12:14,221 - modules.ocr_extractor - INFO - Starting OCR extraction...\r\n2025"}
{"timestamp":"2025-07-26T05:12:14.707Z","level":"ERROR","message":"Analysis failed for upload 6","error":"Python analysis failed: 2025-07-26 12:12:12,380 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_7405844f-5905-46c7-bc3a-49793d1764a6.png\r\n2025-07-26 12:12:12,380 - __main__ - INFO - Step 1: Processing image...\r\n2025-07-26 12:12:12,393 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)\r\n2025-07-26 12:12:14,220 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:12:14,221 - modules.ocr_extractor - INFO - Starting OCR extraction...\r\n2025-07-26 12:12:14,317 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information.\r\n2025-07-26 12:12:14,317 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 12:12:14,318 - modules.ocr_extractor - WARNING - No timeframe found in text\r\n2025-07-26 12:12:14,318 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN\r\n2025-07-26 12:12:14,319 - __main__ - INFO - Step 3: Analyzing price action...\r\n2025-07-26 12:12:14,319 - modules.price_action_analyzer - INFO - Starting price action analysis...\r\n2025-07-26 12:12:14,327 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns\r\n2025-07-26 12:12:14,419 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL\r\n2025-07-26 12:12:14,420 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:12:14,421 - modules.smc_analyzer - INFO - Starting SMC analysis...\r\n2025-07-26 12:12:14,428 - modules.smc_analyzer - INFO - Detected 1 order blocks\r\n2025-07-26 12:12:14,438 - modules.smc_analyzer - INFO - Identified 0 liquidity pools\r\n2025-07-26 12:12:14,464 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps\r\n2025-07-26 12:12:14,464 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:12:14,464 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:12:14,464 - modules.technical_indicators - INFO - Starting technical indicators calculation...\r\n2025-07-26 12:12:14,549 - modules.technical_indicators - INFO - Technical indicators calculation completed\r\n2025-07-26 12:12:14,549 - __main__ - INFO - Step 6: Performing advanced signal analysis...\r\n2025-07-26 12:12:14,550 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis...\r\n2025-07-26 12:12:14,582 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals\r\n2025-07-26 12:12:14,582 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:12:14,582 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:12:14,582 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:12:14,583 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:12:14,583 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:12:14,583 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.05 confidence (very_low)\r\n2025-07-26 12:12:14,583 - __main__ - INFO - Analysis completed successfully\r\n"}
{"timestamp":"2025-07-26T05:15:10.958Z","level":"INFO","message":"File uploaded successfully: chart_1db0d30a-c5d1-469e-9d08-d0b4b261cba9.png","originalName":"image.png","size":314582,"mimetype":"image/png"}
{"timestamp":"2025-07-26T05:15:10.963Z","level":"INFO","message":"Upload record created with ID: 7"}
{"timestamp":"2025-07-26T05:15:10.975Z","level":"INFO","message":"Starting analysis for upload 7"}
{"timestamp":"2025-07-26T05:15:10.976Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_1db0d30a-c5d1-469e-9d08-d0b4b261cba9.png 7"}
{"timestamp":"2025-07-26T05:15:11.556Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:11,556 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_1db0d30a-c5d1-469e-9d08-d0b4b261cba9.png\r\n2025-07-26 12:15:11,556 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-26T05:15:11.571Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:11,570 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-26T05:15:13.393Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,392 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:15:13,392 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-26T05:15:13.488Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,488 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-26T05:15:13.489Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,489 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 12:15:13,489 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-26T05:15:13.489Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,489 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-26T05:15:13.490Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,490 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-26T05:15:13.491Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,490 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-26T05:15:13.497Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,497 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-26T05:15:13.577Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,577 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-26T05:15:13.578Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,577 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:15:13,577 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-26T05:15:13.582Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,581 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-26T05:15:13.588Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,588 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-26T05:15:13.612Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,612 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-26T05:15:13.614Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,613 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:15:13,613 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:15:13,613 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-26T05:15:13.691Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,691 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-26T05:15:13.694Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,691 - __main__ - INFO - Step 6: Performing advanced signal analysis...\r\n2025-07-26 12:15:13,691 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis..."}
{"timestamp":"2025-07-26T05:15:13.725Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,725 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals"}
{"timestamp":"2025-07-26T05:15:13.727Z","level":"WARN","message":"Python stderr: 2025-07-26 12:15:13,725 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:15:13,725 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:15:13,725 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:15:13,725 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:15:13,726 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:15:13,726 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.05 confidence (very_low)\r\n2025-07-26 12:15:13,726 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-26T05:15:13.729Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": false,\r\n  \"error\": \"Unexpected error: Object of type intc is not JSON serializable\",\r\n  \"timestamp\": \"2025-07-26T12:15:13.726361\",\r\n  \"traceback\": \"Traceback (most recent call last):\\n  File \\\"D:\\\\Vicky\\\\project baru\\\\auto-analysis\\\\python_analysis\\\\main.py\\\", line 207, in main\\n    print(json.dumps(results, indent=2))\\n          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\__init__.py\\\", line 238, in dumps\\n    **kw).encode(obj)\\n          ~~~~~~^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 200, in encode\\n    chunks = self.iterencode(o, _one_shot=True)\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 261, in iterencode\\n    return _iterencode(o, 0)\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 180, in default\\n    raise TypeError(f'Object of type {o.__class__.__name__} '\\n                    f'is not JSON serializable')\\nTypeError: Object of type intc is not JSON serializable\\n\"\r\n}"}
{"timestamp":"2025-07-26T05:15:13.811Z","level":"INFO","message":"Python process exited with code 1"}
{"timestamp":"2025-07-26T05:15:13.812Z","level":"ERROR","message":"Python analysis failed","code":1,"stdout":"{\r\n  \"success\": false,\r\n  \"error\": \"Unexpected error: Object of type intc is not JSON serializable\",\r\n  \"timestamp\": \"2025-07-26T12:15:13.726361\",\r\n  \"traceback\": \"Traceback (most recent call last):\\n  File \\\"D:\\\\Vicky\\\\project baru\\\\auto-analysis\\\\python_analysis\\\\main.py\\\", line 207, in main\\n    print(json.dumps(results, indent=2))\\n          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\__init__.py\\\", line 238, in dumps\\n    **kw).encode(obj)\\n          ~~~~~~^^^^^\\n  Fil","stderr":"2025-07-26 12:15:11,556 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_1db0d30a-c5d1-469e-9d08-d0b4b261cba9.png\r\n2025-07-26 12:15:11,556 - __main__ - INFO - Step 1: Processing image...\r\n2025-07-26 12:15:11,570 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)\r\n2025-07-26 12:15:13,392 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:15:13,392 - modules.ocr_extractor - INFO - Starting OCR extraction...\r\n2025"}
{"timestamp":"2025-07-26T05:15:13.812Z","level":"ERROR","message":"Analysis failed for upload 7","error":"Python analysis failed: 2025-07-26 12:15:11,556 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_1db0d30a-c5d1-469e-9d08-d0b4b261cba9.png\r\n2025-07-26 12:15:11,556 - __main__ - INFO - Step 1: Processing image...\r\n2025-07-26 12:15:11,570 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)\r\n2025-07-26 12:15:13,392 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:15:13,392 - modules.ocr_extractor - INFO - Starting OCR extraction...\r\n2025-07-26 12:15:13,488 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information.\r\n2025-07-26 12:15:13,489 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 12:15:13,489 - modules.ocr_extractor - WARNING - No timeframe found in text\r\n2025-07-26 12:15:13,489 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN\r\n2025-07-26 12:15:13,490 - __main__ - INFO - Step 3: Analyzing price action...\r\n2025-07-26 12:15:13,490 - modules.price_action_analyzer - INFO - Starting price action analysis...\r\n2025-07-26 12:15:13,497 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns\r\n2025-07-26 12:15:13,577 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL\r\n2025-07-26 12:15:13,577 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:15:13,577 - modules.smc_analyzer - INFO - Starting SMC analysis...\r\n2025-07-26 12:15:13,581 - modules.smc_analyzer - INFO - Detected 1 order blocks\r\n2025-07-26 12:15:13,588 - modules.smc_analyzer - INFO - Identified 0 liquidity pools\r\n2025-07-26 12:15:13,612 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps\r\n2025-07-26 12:15:13,613 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:15:13,613 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:15:13,613 - modules.technical_indicators - INFO - Starting technical indicators calculation...\r\n2025-07-26 12:15:13,691 - modules.technical_indicators - INFO - Technical indicators calculation completed\r\n2025-07-26 12:15:13,691 - __main__ - INFO - Step 6: Performing advanced signal analysis...\r\n2025-07-26 12:15:13,691 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis...\r\n2025-07-26 12:15:13,725 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals\r\n2025-07-26 12:15:13,725 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:15:13,725 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:15:13,725 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:15:13,725 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:15:13,726 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:15:13,726 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.05 confidence (very_low)\r\n2025-07-26 12:15:13,726 - __main__ - INFO - Analysis completed successfully\r\n"}
{"timestamp":"2025-07-26T05:17:24.243Z","level":"INFO","message":"File uploaded successfully: chart_f64c1898-efb2-4936-9ed3-82ba6bf0cc7e.png","originalName":"image.png","size":314582,"mimetype":"image/png"}
{"timestamp":"2025-07-26T05:17:24.248Z","level":"INFO","message":"Upload record created with ID: 8"}
{"timestamp":"2025-07-26T05:17:24.270Z","level":"INFO","message":"Starting analysis for upload 8"}
{"timestamp":"2025-07-26T05:17:24.271Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f64c1898-efb2-4936-9ed3-82ba6bf0cc7e.png 8"}
{"timestamp":"2025-07-26T05:17:24.903Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:24,902 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f64c1898-efb2-4936-9ed3-82ba6bf0cc7e.png\r\n2025-07-26 12:17:24,902 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-26T05:17:24.915Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:24,915 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-26T05:17:26.841Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,840 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-26T05:17:26.842Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,840 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-26T05:17:26.935Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,934 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information.\r\n2025-07-26 12:17:26,934 - modules.ocr_extractor - WARNING - No trading symbol found in text"}
{"timestamp":"2025-07-26T05:17:26.935Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,935 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-26T05:17:26.936Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,935 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-26T05:17:26.937Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,936 - __main__ - INFO - Step 3: Analyzing price action...\r\n2025-07-26 12:17:26,936 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-26T05:17:26.944Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:26,944 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-26T05:17:27.023Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,022 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-26T05:17:27.024Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,022 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:17:27,022 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-26T05:17:27.029Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,029 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-26T05:17:27.036Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,035 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-26T05:17:27.059Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,057 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-26T05:17:27.062Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,058 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:17:27,058 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:17:27,058 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-26T05:17:27.134Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,133 - modules.technical_indicators - INFO - Technical indicators calculation completed\r\n2025-07-26 12:17:27,133 - __main__ - INFO - Step 6: Performing advanced signal analysis..."}
{"timestamp":"2025-07-26T05:17:27.137Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,133 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis..."}
{"timestamp":"2025-07-26T05:17:27.170Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,169 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals"}
{"timestamp":"2025-07-26T05:17:27.171Z","level":"WARN","message":"Python stderr: 2025-07-26 12:17:27,169 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:17:27,169 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:17:27,169 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:17:27,169 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:17:27,170 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:17:27,170 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.05 confidence (very_low)\r\n2025-07-26 12:17:27,170 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-26T05:17:27.175Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": false,\r\n  \"error\": \"Unexpected error: Object of type intc is not JSON serializable\",\r\n  \"timestamp\": \"2025-07-26T12:17:27.170748\",\r\n  \"traceback\": \"Traceback (most recent call last):\\n  File \\\"D:\\\\Vicky\\\\project baru\\\\auto-analysis\\\\python_analysis\\\\main.py\\\", line 207, in main\\n    print(json.dumps(results, indent=2))\\n          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\__init__.py\\\", line 238, in dumps\\n    **kw).encode(obj)\\n          ~~~~~~^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 200, in encode\\n    chunks = self.iterencode(o, _one_shot=True)\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 261, in iterencode\\n    return _iterencode(o, 0)\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\encoder.py\\\", line 180, in default\\n    raise TypeError(f'Object of type {o.__class__.__name__} '\\n                    f'is not JSON serializable')\\nTypeError: Object of type intc is not JSON serializable\\n\"\r\n}"}
{"timestamp":"2025-07-26T05:17:27.286Z","level":"INFO","message":"Python process exited with code 1"}
{"timestamp":"2025-07-26T05:17:27.286Z","level":"ERROR","message":"Python analysis failed","code":1,"stdout":"{\r\n  \"success\": false,\r\n  \"error\": \"Unexpected error: Object of type intc is not JSON serializable\",\r\n  \"timestamp\": \"2025-07-26T12:17:27.170748\",\r\n  \"traceback\": \"Traceback (most recent call last):\\n  File \\\"D:\\\\Vicky\\\\project baru\\\\auto-analysis\\\\python_analysis\\\\main.py\\\", line 207, in main\\n    print(json.dumps(results, indent=2))\\n          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^\\n  File \\\"C:\\\\Python313\\\\Lib\\\\json\\\\__init__.py\\\", line 238, in dumps\\n    **kw).encode(obj)\\n          ~~~~~~^^^^^\\n  Fil","stderr":"2025-07-26 12:17:24,902 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f64c1898-efb2-4936-9ed3-82ba6bf0cc7e.png\r\n2025-07-26 12:17:24,902 - __main__ - INFO - Step 1: Processing image...\r\n2025-07-26 12:17:24,915 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)\r\n2025-07-26 12:17:26,840 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:17:26,840 - modules.ocr_extractor - INFO - Starting OCR extraction...\r\n2025"}
{"timestamp":"2025-07-26T05:17:27.287Z","level":"ERROR","message":"Analysis failed for upload 8","error":"Python analysis failed: 2025-07-26 12:17:24,902 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f64c1898-efb2-4936-9ed3-82ba6bf0cc7e.png\r\n2025-07-26 12:17:24,902 - __main__ - INFO - Step 1: Processing image...\r\n2025-07-26 12:17:24,915 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)\r\n2025-07-26 12:17:26,840 - __main__ - INFO - Step 2: Extracting OCR data...\r\n2025-07-26 12:17:26,840 - modules.ocr_extractor - INFO - Starting OCR extraction...\r\n2025-07-26 12:17:26,934 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information.\r\n2025-07-26 12:17:26,934 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 12:17:26,935 - modules.ocr_extractor - WARNING - No timeframe found in text\r\n2025-07-26 12:17:26,935 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN\r\n2025-07-26 12:17:26,936 - __main__ - INFO - Step 3: Analyzing price action...\r\n2025-07-26 12:17:26,936 - modules.price_action_analyzer - INFO - Starting price action analysis...\r\n2025-07-26 12:17:26,944 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns\r\n2025-07-26 12:17:27,022 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL\r\n2025-07-26 12:17:27,022 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:17:27,022 - modules.smc_analyzer - INFO - Starting SMC analysis...\r\n2025-07-26 12:17:27,029 - modules.smc_analyzer - INFO - Detected 1 order blocks\r\n2025-07-26 12:17:27,035 - modules.smc_analyzer - INFO - Identified 0 liquidity pools\r\n2025-07-26 12:17:27,057 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps\r\n2025-07-26 12:17:27,058 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:17:27,058 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:17:27,058 - modules.technical_indicators - INFO - Starting technical indicators calculation...\r\n2025-07-26 12:17:27,133 - modules.technical_indicators - INFO - Technical indicators calculation completed\r\n2025-07-26 12:17:27,133 - __main__ - INFO - Step 6: Performing advanced signal analysis...\r\n2025-07-26 12:17:27,133 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis...\r\n2025-07-26 12:17:27,169 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals\r\n2025-07-26 12:17:27,169 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:17:27,169 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:17:27,169 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:17:27,169 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:17:27,170 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:17:27,170 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.05 confidence (very_low)\r\n2025-07-26 12:17:27,170 - __main__ - INFO - Analysis completed successfully\r\n"}
{"timestamp":"2025-07-26T05:21:49.957Z","level":"INFO","message":"File uploaded successfully: chart_0b40c0d2-a998-4ae3-ae11-a8396b5d4bf5.png","originalName":"image.png","size":314582,"mimetype":"image/png"}
{"timestamp":"2025-07-26T05:21:49.961Z","level":"INFO","message":"Upload record created with ID: 9"}
{"timestamp":"2025-07-26T05:21:50.035Z","level":"INFO","message":"Starting analysis for upload 9"}
{"timestamp":"2025-07-26T05:21:50.036Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_0b40c0d2-a998-4ae3-ae11-a8396b5d4bf5.png 9"}
{"timestamp":"2025-07-26T05:21:50.602Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:50,602 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_0b40c0d2-a998-4ae3-ae11-a8396b5d4bf5.png"}
{"timestamp":"2025-07-26T05:21:50.603Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:50,602 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-26T05:21:50.615Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:50,615 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-26T05:21:52.497Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:52,496 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-26T05:21:52.498Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:52,497 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-26T05:21:53.648Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,648 - modules.ocr_extractor - INFO - Found symbol: XAUUSD"}
{"timestamp":"2025-07-26T05:21:53.649Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,648 - modules.ocr_extractor - INFO - Found timeframe: M1\r\n2025-07-26 12:21:53,648 - modules.ocr_extractor - INFO - Extracted 18 price values\r\n2025-07-26 12:21:53,649 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: XAUUSD, Timeframe: M1"}
{"timestamp":"2025-07-26T05:21:53.651Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,650 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-26T05:21:53.651Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,650 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-26T05:21:53.657Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,657 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-26T05:21:53.741Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,741 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-26T05:21:53.742Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,741 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 12:21:53,741 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-26T05:21:53.745Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,745 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-26T05:21:53.754Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,753 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-26T05:21:53.775Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,775 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-26T05:21:53.779Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,775 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 12:21:53,775 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 12:21:53,775 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-26T05:21:53.851Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,849 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-26T05:21:53.853Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,850 - __main__ - INFO - Step 6: Performing advanced signal analysis...\r\n2025-07-26 12:21:53,850 - modules.advanced_signal_analyzer - INFO - Starting advanced signal analysis..."}
{"timestamp":"2025-07-26T05:21:53.881Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,880 - modules.advanced_signal_analyzer - INFO - Advanced signal analysis completed. Found 1 high-quality signals"}
{"timestamp":"2025-07-26T05:21:53.884Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"9\",\r\n  \"timestamp\": \"2025-07-26T12:21:53.881370\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"ec QxauusDG 1 5m15m30mthfjdiiIndicators83GYAlertdRepaya- gao8Be Omit-/aOOO rPubtishJ EI ATSPra Oe EE Ceseeegmednsian ar 1 qm a a mh Ae 3,362.00 5 ya ih - o- N ik iy Fl ti h, 3,360.00 ce ge i ll i s i H H 3,358.00 i i il : D, we i J yo 3,356.00 T way 3,354.00 G 3,352.00 oy il : 3,350.00 b 804 i Ih Mt Hy ned : 3,348.00 e Ln Hatha : Ml nh il f , 3,346.00 a M4 th 7 i iN 3,244.00 Gy i , ul, ih i a h H I, soa oO Wy i i t 3,240.00 , th Mi 3,338.00 , hy ligt 3,334.00 : I y i si WY. 3,332.00 : PA : rm i iN N 3,330.00 , Tk 3,328.00 IW 5 3,326.00 /rnnnnnnanaunanaaaauaaanaauanaaanauanaanaaanananananaannanannannnnn aan IfTradingView : . g 5 3,322.00 4:0D04:30O5:0005:3006:0006:3007:0007:3008:0D08:3009:0009:3010:0010:3011:00Fre25Jut251%:4512:30120D13:3014200914:3015:0015:3016:0016:2017:0017:3018:0D18:3019:0019:30 1D5D1M3M6MYTD1Y5YAuS9 17:47:05UTC a PineEditor StrategyTester ReplayTrading TradingPanel NO 625\",\r\n    \"symbol\": \"XAUUSD\",\r\n    \"timeframe\": \"M1\",\r\n    \"current_price\": 362.0,\r\n    \"price_data\": {\r\n      \"current\": 362.0,\r\n      \"high\": 362.0,\r\n      \"low\": 240.0,\r\n      \"open\": 240.0,\r\n      \"close\": 362.0\r\n    },\r\n    \"chart_metadata\": {\r\n      \"timestamps\": [\r\n        \"04:30\",\r\n        \"5:00\",\r\n        \"05:30\",\r\n        \"06:00\",\r\n        \"06:30\",\r\n        \"07:00\",\r\n        \"07:30\",\r\n        \"08:30\",\r\n        \"09:00\",\r\n        \"09:30\",\r\n        \"10:00\",\r\n        \"10:30\",\r\n        \"11:00\",\r\n        \"12:30\",\r\n        \"13:30\",\r\n        \"14:30\",\r\n        \"15:00\",\r\n        \"15:30\",\r\n        \"16:00\",\r\n        \"16:20\",\r\n        \"17:00\",\r\n        \"17:30\",\r\n        \"18:30\",\r\n        \"19:00\",\r\n        \"19:30\",\r\n        \"17:47:05\"\r\n      ]\r\n    }\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.19576480000000002,\r\n    \"confidence_level\": \"very_low\",\r\n    \"confidence_description\": \"Sangat Rendah - Tidak ada sinyal yang jelas\",\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"position_size\": 0.01,\r\n    \"market_regime\": \"volatile\",\r\n    \"signal_reasoning\": {\r\n      \"signal_explanations\": [],\r\n      \"confluence_reasoning\": \"Confluence lemah (0.00) dengan sinyal yang bertentangan. Tidak disarankan untuk trading dalam kondisi ini.\",\r\n      \"market_context_impact\": \"Kondisi pasar saat ini (volatile): Pasar sangat volatile dengan noise tinggi. Hal ini tidak mendukung sinyal yang terdeteksi.\",\r\n      \"strength_analysis\": {},\r\n      \"risk_assessment\": [\r\n        {\r\n          \"factor\": \"Pasar volatile - risiko whipsaw tinggi\",\r\n          \"impact\": \"medium\",\r\n          \"mitigation\": \"Gunakan position size yang lebih kecil dan stop loss yang lebih ketat\"\r\n        },\r\n        {\r\n          \"factor\": \"Confluence rendah - validasi sinyal terbatas\",\r\n          \"impact\": \"medium\",\r\n          \"mitigation\": \"Monitor kondisi pasar dan gunakan risk management yang ketat\"\r\n        }\r\n      ]\r\n    },\r\n    \"confluence_analysis\": {\r\n      \"confluence_score\": 0.0,\r\n      \"confluence_level\": \"none\",\r\n      \"confluent_signals\": [],\r\n      \"conflicting_signals\": [],\r\n      \"signal_consensus\": \"NEUTRAL\"\r\n    },\r\n    \"execution_plan\": {\r\n      \"action\": \"HOLD\",\r\n      \"execution_steps\": [\r\n        \"Tunggu setup yang lebih baik\",\r\n        \"Monitor kondisi pasar\"\r\n      ],\r\n      \"timing_guidance\": \"Tidak ada timing khusus - tunggu sinyal yang lebih kuat\",\r\n      \"entry_strategy\": \"Tidak ada entry saat ini\",\r\n      \"exit_strategy\": \"Tidak applicable\",\r\n      \"monitoring_points\": [\r\n        \"Perubahan market regime\",\r\n        \"Munculnya sinyal baru\"\r\n      ]\r\n    },\r\n    \"risk_factors\": [\r\n      \"Pasar volatile - risiko whipsaw tinggi\",\r\n      \"Confluence rendah - validasi sinyal terbatas\"\r\n    ],\r\n    \"supporting_factors\": [],\r\n    \"key_levels\": [],\r\n    \"timeframe_analysis\": {\r\n      \"current_timeframe\": \"M1\",\r\n      \"suitability_scores\": {\r\n        \"day_trading\": 0.9,\r\n        \"swing_trading\": 0.1,\r\n        \"scalping\": 0.5\r\n      },\r\n      \"recommended_style\": \"day_trading\",\r\n      \"style_confidence\": 0.9,\r\n      \"timeframe_notes\": [\r\n        \"Cocok untuk scalping\",\r\n        \"Perlu reaksi cepat\",\r\n        \"Spread impact tinggi\",\r\n        \"Market volatile - pertimbangkan timeframe lebih tinggi\"\r\n      ]\r\n    },\r\n    \"scenario_analysis\": {\r\n      \"bullish\": {\r\n        \"probability\": 0.0,\r\n        \"description\": \"Skenario bullish dengan probabilitas 0.0%\",\r\n        \"key_factors\": [],\r\n        \"price_targets\": [],\r\n        \"conditions\": [\r\n          \"Break above resistance\",\r\n          \"Volume confirmation\",\r\n          \"Momentum continuation\"\r\n        ]\r\n      },\r\n      \"bearish\": {\r\n        \"probability\": 0.0,\r\n        \"description\": \"Skenario bearish dengan probabilitas 0.0%\",\r\n        \"key_factors\": [],\r\n        \"price_targets\": [],\r\n        \"conditions\": [\r\n          \"Break below support\",\r\n          \"Volume confirmation\",\r\n          \"Momentum continuation\"\r\n        ]\r\n      },\r\n      \"neutral\": {\r\n        \"probability\": 1.0,\r\n        \"description\": \"Skenario sideways dengan probabilitas 100.0%\",\r\n        \"key_factors\": [\r\n          \"Mixed signals\",\r\n          \"Low momentum\",\r\n          \"Range-bound market\"\r\n        ],\r\n        \"price_targets\": [],\r\n        \"conditions\": [\r\n          \"Respect of support/resistance\",\r\n          \"Low volume\",\r\n          \"Consolidation pattern\"\r\n        ]\r\n      }\r\n    },\r\n    \"advanced_patterns\": [\r\n      {\r\n        \"pattern\": \"double_top\",\r\n        \"confidence\": 1.0,\r\n        \"strength\": 0.8,\r\n        \"reliability\": 0.75,\r\n        \"reversal\": true,\r\n        \"key_levels\": {\r\n          \"resistance\": 589,\r\n          \"support\": 14,\r\n          \"neckline\": 14\r\n        },\r\n        \"signal_type\": \"SELL\",\r\n        \"description\": \"Double top pattern detected at 589.00000 level\",\r\n        \"pattern_points\": {\r\n          \"peak1\": {\r\n            \"timestamp\": 30,\r\n            \"price\": 587\r\n          },\r\n          \"peak2\": {\r\n            \"timestamp\": 692,\r\n            \"price\": 589\r\n          },\r\n          \"valley\": {\r\n            \"price\": 14\r\n          }\r\n        }\r\n      },\r\n      {\r\n        \"pattern\": \"double_bottom\",\r\n        \"confidence\": 1.0,\r\n        \"strength\": 0.8,\r\n        \"reliability\": 0.75,\r\n        \"reversal\": true,\r\n        \"key_levels\": {\r\n          \"support\": 64,\r\n          \"resistance\": 948,\r\n          \"neckline\": 948\r\n        },\r\n        \"signal_type\": \"BUY\",\r\n        \"description\": \"Double bottom pattern detected at 64.00000 level\",\r\n        \"pattern_points\": {\r\n          \"trough1\": {\r\n            \"timestamp\": 213,\r\n            \"price\": 65\r\n          },\r\n          \"trough2\": {\r\n            \"timestamp\": 349,\r\n            \"price\": 64\r\n          },\r\n          \"peak\": {\r\n            \"price\": 948\r\n          }\r\n        }\r\n      }\r\n    ],\r\n    \"multi_timeframe_analysis\": {\r\n      \"current_timeframe\": \"M1\",\r\n      \"higher_timeframe\": {\r\n        \"timeframe\": \"M15\",\r\n        \"trend\": \"RANGING\",\r\n        \"strength\": 0.7,\r\n        \"support_resistance\": \"STRONG\",\r\n        \"momentum\": \"STABLE\"\r\n      },\r\n      \"lower_timeframe\": {\r\n        \"timeframe\": \"M1\",\r\n        \"trend\": \"CHOPPY\",\r\n        \"strength\": 0.4,\r\n        \"noise_level\": \"HIGH\",\r\n        \"volatility\": \"ELEVATED\"\r\n      },\r\n      \"alignment_score\": 0.3,\r\n      \"alignment_description\": \"Poor alignment - conflicting timeframes, avoid trading\",\r\n      \"trading_bias\": \"NEUTRAL\",\r\n      \"confidence_multiplier\": 0.7\r\n    },\r\n    \"signal_quality_metrics\": {\r\n      \"average_quality\": 0.6160003986237987,\r\n      \"quality_distribution\": {\r\n        \"high\": 0,\r\n        \"medium\": 1,\r\n        \"low\": 0\r\n      },\r\n      \"total_signals\": 1,\r\n      \"highest_quality\": 0.6160003986237987,\r\n      \"lowest_quality\": 0.6160003986237987\r\n    },\r\n    \"analysis_score\": {\r\n      \"overall_score\": 0.6680001993118994,\r\n      \"grade\": \"C\",\r\n      \"description\": \"Fair - Setup trading acceptable\",\r\n      \"component_scores\": {\r\n        \"signal_quality\": 0.6160003986237987,\r\n        \"pattern_quality\": 1.0,\r\n        \"mtf_alignment\": 0.3\r\n      }\r\n    },\r\n    \"recommendation_confidence\": {\r\n      \"confidence\": 0.4312002790366591,\r\n      \"level\": \"low\",\r\n      \"factors\": [],\r\n      \"base_confidence\": 0.6160003986237987,\r\n      \"mtf_adjustment\": 0.7\r\n    },\r\n    \"risk_assessment\": {\r\n      \"risk_level\": \"high\",\r\n      \"risk_factors\": [\r\n        \"Single signal - lack of confirmation\",\r\n        \"Reversal patterns detected - trend change possible\"\r\n      ],\r\n      \"risk_score\": 0.4,\r\n      \"mitigation_suggestions\": [\r\n        \"Wait for additional confirmation signals\",\r\n        \"Use tighter stop losses and smaller position sizes\"\r\n      ]\r\n    },\r\n    \"execution_timing\": {\r\n      \"timing\": \"wait_for_confirmation\",\r\n      \"reason\": \"Moderate signals - wait for additional confirmation\",\r\n      \"urgency\": \"low\",\r\n      \"signal_strength\": 0.6160003986237987,\r\n      \"mtf_alignment\": 0.3\r\n    },\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 3195.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.639\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 18505.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"smc\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3135,\r\n      \"signal_line\": 0.3135,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\",\r\n    \"analysis_version\": \"enhanced_v2.0\",\r\n    \"enhanced_features\": [\r\n      \"Advanced pattern recognition\",\r\n      \"Multi-timeframe analysis simulation\",\r\n      \"Signal confluence analysis\",\r\n      \"Enhanced risk management\",\r\n      \"Detailed signal reasoning\",\r\n      \"Market regime detection\"\r\n    ]\r\n  }\r\n}"}
{"timestamp":"2025-07-26T05:21:53.885Z","level":"WARN","message":"Python stderr: 2025-07-26 12:21:53,880 - __main__ - INFO - Step 7: Making enhanced trading decision...\r\n2025-07-26 12:21:53,880 - modules.decision_engine - INFO - Starting enhanced decision making process...\r\n2025-07-26 12:21:53,880 - modules.decision_engine - INFO - Market regime detected: volatile\r\n2025-07-26 12:21:53,881 - modules.decision_engine - INFO - Validated 0 out of 2 signals\r\n2025-07-26 12:21:53,881 - modules.decision_engine - ERROR - Enhanced key levels identification failed: unsupported format string passed to dict.__format__\r\n2025-07-26 12:21:53,881 - modules.decision_engine - INFO - Enhanced decision completed: HOLD with 0.20 confidence (very_low)\r\n2025-07-26 12:21:53,881 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-26T05:21:53.965Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-26T05:21:53.965Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-26T05:21:53.974Z","level":"INFO","message":"Analysis completed for upload 9"}
